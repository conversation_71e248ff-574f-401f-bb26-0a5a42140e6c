"use client";

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { PackageInfo, AnalysisSummary } from "@/types/package";

/**
 * Props for the usePackageAnalysis hook with accessibility considerations.
 * 
 * @interface UsePackageAnalysisProps
 * @description Defines the callback interface for the package analysis hook.
 * Callbacks should handle accessibility announcements and error communication
 * appropriately for screen reader users.
 * 
 * @accessibility
 * - Analysis completion should be announced to screen readers
 * - Progress updates should not overwhelm assistive technology
 * - Error messages should be clear, actionable, and immediately announced
 * - Success states should provide meaningful feedback
 * 
 * @wcag
 * - 3.3.1 Error Identification: Errors are clearly identified and described
 * - 3.3.3 Error Suggestion: Error messages provide suggestions when possible
 * - 4.1.3 Status Messages: Important status changes are announced
 * 
 * @example
 * `	sx
 * const { analyzePackages } = usePackageAnalysis({
 *   onAnalysisComplete: (results, summary) => {
 *     // Announce completion: "Analysis complete. Found X packages, Y need updates"
 *     setResults(results);
 *     setSummary(summary);
 *   },
 *   onError: (error) => {
 *     // Announce error immediately: "Analysis failed: [error message]"
 *     setError(error);
 *   }
 * });
 * `
 */
interface UsePackageAnalysisProps {
  /** 
   * Callback fired when package analysis completes successfully.
   * @param results - Array of analyzed package information
   * @param summary - Statistical summary of the analysis
   * @accessibility 
   * - Should announce completion with summary statistics
   * - Consider announcing number of packages that need attention
   * - Avoid overwhelming users with too much detail in announcement
   */
  onAnalysisComplete: (results: PackageInfo[], summary: AnalysisSummary) => void;
  
  /** 
   * Callback fired when package analysis encounters an error.
   * @param error - Human-readable error message
   * @accessibility 
   * - Error should be announced immediately via aria-live region
   * - Message should be clear and actionable when possible
   * - Should not use technical jargon that confuses users
   */
  onError: (error: string) => void;
}

export function usePackageAnalysis({ onAnalysisComplete, onError }: UsePackageAnalysisProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const { toast } = useToast();

  const analyzePackages = async (content: string, fileName: string) => {
    if (!content.trim()) {
      onError("Por favor, forneça o conteúdo do arquivo");
      return;
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch("/api/analyze-packages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content,
          fileName,
        }),
      });

      clearInterval(progressInterval);
      setAnalysisProgress(100);

      if (!response.ok) {
        throw new Error("Erro ao analisar pacotes");
      }

      const data = await response.json();
      
      if (data.packages?.length === 0) {
        onError("Nenhum pacote encontrado no arquivo");
      } else {
        onAnalysisComplete(data.packages || [], data.summary || null);
        toast({
          title: "Análise concluída",
          description: `Encontrados ${data.packages?.length || 0} pacotes`,
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erro desconhecido";
      onError(errorMessage);
      toast({
        title: "Erro na análise",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setTimeout(() => {
        setIsAnalyzing(false);
        setAnalysisProgress(0);
      }, 500);
    }
  };

  return {
    isAnalyzing,
    analysisProgress,
    analyzePackages,
  };
}
