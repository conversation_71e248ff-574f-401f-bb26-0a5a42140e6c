"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle2<PERSON><PERSON>,
  Clock,
  ArrowUpRight,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PackageInfo } from "@/types/package";
import { getPackageUrl, getStatusColor } from "@/lib/package-utils";
import { ExternalLink, TrendingUp } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";

/**
 * Props for the PackageCard component with comprehensive accessibility support.
 *
 * @interface PackageCardProps
 * @description Defines the interface for displaying individual package information
 * in a card format. Each card presents package details with clear visual hierarchy
 * and accessible status indicators.
 *
 * @accessibility
 * - Status should be conveyed through multiple channels (color, icon, text)
 * - External links should be clearly marked and have descriptive labels
 * - Version information should be clearly structured and readable
 * - Interactive elements should have proper focus indicators
 * - Package manager badges should have meaningful labels
 *
 * @wcag
 * - 1.4.1 Use of Color: Status not conveyed by color alone
 * - 1.4.3 Contrast: Sufficient color contrast for text and backgrounds
 * - 2.4.4 Link Purpose: Link purpose clear from link text or context
 * - 3.2.4 Consistent Identification: Consistent identification of components
 *
 * @example
 * `	sx
 * <PackageCard
 *   pkg={{
 *     name: "react",
 *     currentVersion: "18.0.0",
 *     latestVersion: "18.2.0",
 *     status: "outdated",
 *     packageManager: "npm",
 *     homepage: "https://reactjs.org",
 *     description: "A JavaScript library for building user interfaces"
 *   }}
 * />
 * `
 */
interface PackageCardProps {
  /**
   * Package information object containing all display data.
   * @accessibility
   * - Package name should be the primary heading/identifier
   * - Status should be clearly indicated with icon, color, and text
   * - Version comparison should be easy to understand
   * - Homepage links should open in new tab with proper ARIA labels
   * - Description should be concise and informative
   * - Package manager should be visually distinct but not rely on color alone
   */
  pkg: PackageInfo;
}

export function PackageCard({ pkg }: PackageCardProps) {
  const packageUrl = getPackageUrl(pkg);

  const getStatusIcon = () => {
    switch (pkg.status) {
      case "up-to-date":
        return (
          <CheckCircle2Icon className="w-5 h-5 text-green-600 dark:text-green-400" />
        );
      case "outdated":
        return <Clock className="w-5 h-5 text-amber-600 dark:text-amber-400" />;
      case "error":
        return (
          <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
        );
      default:
        return null;
    }
  };

  const getPackageManagerIcon = () => {
    const iconMap = {
      npm: "/logos/npm.svg",
      pip: "/logos/pypi.svg",
      pub: "/logos/pub.svg",
    };

    const iconSrc = iconMap[pkg.packageManager as keyof typeof iconMap];

    if (!iconSrc) {
      return null;
    }

    return (
      <a
        href={packageUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="group relative hover:scale-105 transition-all duration-200 rounded-lg p-1 hover:bg-muted/50"
        title={`View ${pkg.name} on ${pkg.packageManager}`}
        aria-label={`Open ${pkg.name} package page on ${pkg.packageManager}`}
      >
        <Image
          src={iconSrc}
          alt={`${pkg.packageManager} logo`}
          width={32}
          height={32}
          className="w-8 h-8 sm:w-10 sm:h-10 transition-all duration-200"
        />
        <ArrowUpRight className="absolute -top-1 -right-1 w-3 h-3 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      </a>
    );
  };

  const getStatusText = () => {
    switch (pkg.status) {
      case "up-to-date":
        return "Up to date";
      case "outdated":
        return "Update available";
      case "error":
        return "Error";
      default:
        return "Unknown";
    }
  };

  return (
    <div
      className={cn(
        "group relative flex flex-col sm:flex-row sm:items-start gap-4 p-4 sm:p-5",
        "border border-border/50 rounded-xl bg-card/50 backdrop-blur-sm",
        "hover:shadow-lg hover:shadow-primary/5 hover:border-primary/20",
        "transition-all duration-300 ease-out",
        "hover:bg-card/80"
      )}
    >
      {/* Status indicator line */}
      <div
        className={cn(
          "absolute left-0 top-0 bottom-0 w-1 rounded-l-xl transition-all duration-300",
          pkg.status === "up-to-date" && "bg-green-500/60",
          pkg.status === "outdated" && "bg-amber-500/60",
          pkg.status === "error" && "bg-red-500/60"
        )}
      />

      <div className="flex items-start gap-4 flex-1 min-w-0">
        <div className="flex-shrink-0 mt-0.5">{getStatusIcon()}</div>

        <div className="flex-1 min-w-0 space-y-3">
          {/* Package name and link */}
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-base sm:text-lg leading-tight">
              <a
                href={packageUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="group/link hover:text-primary transition-colors duration-200 flex items-center gap-2"
                aria-label={`Open ${pkg.name} package page`}
              >
                <span className="truncate">{pkg.name}</span>
                <ExternalLink className="w-4 h-4 flex-shrink-0 opacity-60 group-hover/link:opacity-100 transition-opacity duration-200" />
              </a>
            </h3>
          </div>

          {/* Description */}
          {pkg.description && (
            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">
              {pkg.description}
            </p>
          )}

          {/* Version information */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Current:</span>
              <code className="px-2 py-1 bg-muted/60 rounded-md font-mono text-xs font-medium">
                {pkg.currentVersion}
              </code>
            </div>

            {pkg.status === "outdated" && pkg.latestVersion && (
              <div className="flex items-center gap-2 text-sm">
                <TrendingUp className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                <span className="text-muted-foreground">Latest:</span>
                <code className="px-2 py-1 bg-amber-50 dark:bg-amber-950/30 text-amber-700 dark:text-amber-300 rounded-md font-mono text-xs font-medium border border-amber-200 dark:border-amber-800">
                  {pkg.latestVersion}
                </code>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Status badge and package manager icon */}
      <div className="flex items-center justify-between sm:flex-col sm:items-end gap-3 flex-shrink-0">
        <Badge
          variant="secondary"
          className={cn(
            "text-xs font-medium px-3 py-1.5 rounded-full border transition-all duration-200",
            pkg.status === "up-to-date" &&
              "bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-300 dark:border-green-800",
            pkg.status === "outdated" &&
              "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/30 dark:text-amber-300 dark:border-amber-800",
            pkg.status === "error" &&
              "bg-red-50 text-red-700 border-red-200 dark:bg-red-950/30 dark:text-red-300 dark:border-red-800"
          )}
        >
          {getStatusText()}
        </Badge>

        <div className="flex items-center justify-center">
          {getPackageManagerIcon()}
        </div>
      </div>
    </div>
  );
}
