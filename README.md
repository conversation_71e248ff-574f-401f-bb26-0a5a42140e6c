# 🚀 PackMan - Dependency Analyzer

A modern web application for package dependency analysis, allowing you to quickly check if your dependencies are up-to-date across different development ecosystems.

## ✨ About PackMan

PackMan is a dependency analysis tool that allows developers to:

- **📦 Check outdated packages** in dependency files
- **🔍 Analyze multiple formats** including package.json (npm), requirements.txt (pip), and pubspec.yaml (Flutter/Dart)
- **📊 Visualize statistics** about the state of project dependencies
- **🔄 Get updated information** directly from official package registries

## 🛠️ Technologies Used

### 🎯 Core Framework

- **⚡ Next.js 15** - React framework with App Router
- **📘 TypeScript 5** - Statically typed JavaScript
- **🎨 Tailwind CSS 4** - Utility-first CSS framework

### 🧩 UI Components & Styling

- **🧩 shadcn/ui** - Accessible components based on Radix UI
- **🎯 Lucide React** - Consistent icon library
- **🌈 Framer Motion** - Animation library for React
- **🎨 Next Themes** - Light/dark theme support

### 📋 Forms & Validation

- **🎣 React Hook Form** - Performant form management
- **✅ Zod** - TypeScript schema validation

### 🔄 State Management & Requests

- **🐻 Zustand** - Simple and scalable state management
- **🔄 TanStack Query** - Data synchronization for React
- **🌐 Axios** - Promise-based HTTP client

## 🔍 Key Features

- **📤 File Upload** - Upload dependency files or paste content manually
- **🔄 Automatic Detection** - Automatically identifies file type (npm, pip, pub)
- **📊 Detailed Analysis** - Checks each dependency against the latest available version
- **📈 Statistical Summary** - View how many packages are up-to-date, outdated, or have errors
- **🔗 Documentation Links** - Easily access the official page of each package
- **🌓 Dark/Light Mode** - Interface adaptable to your preferences

## 🚀 How to Use

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

Access [http://localhost:3000](http://localhost:3000) to see the application running.

## 📁 Project Structure

```
src/
├── app/                 # Next.js pages with App Router
│   └── api/            # API endpoints
├── components/          # Reusable React components
│   ├── package-checker/ # Analyzer-specific components
│   └── ui/             # shadcn/ui components
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and services
└── types/              # TypeScript type definitions
```

## 🔌 Supported APIs

PackMan integrates with the following package registries:

- **📦 npm Registry** - For JavaScript/Node.js packages
- **🐍 PyPI** - For Python packages
- **💙 pub.dev** - For Flutter/Dart packages

## 🤝 Contributions

Contributions are welcome! Feel free to open issues or submit pull requests to improve PackMan.

---

Developed with ❤️ for the developer community.
