"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Upload,
  Clipboard,
  Loader2,
  FileText,
  AlertCircle,
  X,
  CheckCircle2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useFileUpload } from "@/hooks/use-file-upload";
import { formatFileSize } from "@/lib/package-utils";

/**
 * Props for the FileUploadSection component with accessibility considerations.
 *
 * @interface FileUploadSectionProps
 * @description Defines the interface for a dual-mode file upload component that supports
 * both file upload and manual text input. Designed with accessibility in mind including
 * proper ARIA labels, keyboard navigation, and screen reader support.
 *
 * @accessibility
 * - File input should have proper labels and accept attributes
 * - Progress indicators must be announced to screen readers
 * - Error messages should be associated with relevant form controls
 * - Tab navigation should be logical and keyboard accessible
 * - Loading states should be clearly communicated
 *
 * @wcag
 * - 2.1.1 Keyboard: All functionality available via keyboard
 * - 3.3.1 Error Identification: Errors are clearly identified
 * - 3.3.2 Labels or Instructions: Form controls have labels
 * - 4.1.3 Status Messages: Status changes are announced
 *
 * @example
 * `	sx
 * <FileUploadSection
 *   fileContent={content}
 *   selectedFile={file}
 *   onFileContentChange={setContent}
 *   onFileSelect={setFile}
 *   onAnalyze={handleAnalyze}
 *   isAnalyzing={false}
 *   analysisProgress={0}
 *   error={null}
 * />
 * `
 */
interface FileUploadSectionProps {
  /**
   * Current text content of the file or manual input.
   * @accessibility Should be properly associated with textarea via labels
   */
  fileContent: string;

  /**
   * Currently selected file object, null if none selected.
   * @accessibility File selection should be announced to screen readers
   */
  selectedFile: File | null;

  /**
   * Callback fired when file content changes (manual input or file upload).
   * @param content - New content string
   * @accessibility Content changes should not interrupt screen reader flow
   */
  onFileContentChange: (content: string) => void;

  /**
   * Callback fired when a file is selected or deselected.
   * @param file - Selected file object or null
   * @accessibility File selection should be announced with file name and size
   */
  onFileSelect: (file: File | null) => void;

  /**
   * Callback fired when the analyze button is clicked.
   * @accessibility Should be clearly labeled and indicate what action will occur
   */
  onAnalyze: () => void;

  /**
   * Whether analysis is currently in progress.
   * @accessibility Loading state should be announced and button should be disabled
   */
  isAnalyzing: boolean;

  /**
   * Current analysis progress as a percentage (0-100).
   * @accessibility Progress should be announced periodically, not on every change
   */
  analysisProgress: number;

  /**
   * Error message to display, null if no error.
   * @accessibility Errors should be announced immediately and associated with relevant controls
   */
  error: string | null;
}

export function FileUploadSection({
  fileContent,
  selectedFile,
  onFileContentChange,
  onFileSelect,
  onAnalyze,
  isAnalyzing,
  analysisProgress,
  error,
}: FileUploadSectionProps) {
  const [dragActive, setDragActive] = useState(false);
  const { handleFileUpload } = useFileUpload({
    onFileContentChange,
    onFileSelect,
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      const event = {
        target: { files: [file] },
      } as any;
      handleFileUpload(event);
    }
  };

  const clearFile = () => {
    onFileSelect(null);
    onFileContentChange("");
  };

  const hasContent = fileContent.trim().length > 0;
  const canAnalyze = hasContent && !isAnalyzing;

  return (
    <div className="space-y-4 sm:space-y-6">
      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-2 h-11 sm:h-12 bg-muted/50">
          <TabsTrigger
            value="upload"
            className="flex items-center gap-2 text-xs sm:text-sm py-2 sm:py-3 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
          >
            <Upload className="w-4 h-4" />
            <span className="hidden sm:inline">File Upload</span>
            <span className="sm:hidden">Upload</span>
          </TabsTrigger>
          <TabsTrigger
            value="manual"
            className="flex items-center gap-2 text-xs sm:text-sm py-2 sm:py-3 data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all"
          >
            <Clipboard className="w-4 h-4" />
            <span className="hidden sm:inline">Paste Content</span>
            <span className="sm:hidden">Paste</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="upload"
          className="space-y-3 sm:space-y-4 mt-4 sm:mt-6"
        >
          <div className="space-y-3">
            <Label
              htmlFor="file-upload"
              className="text-sm sm:text-base font-medium"
            >
              Select dependency file
            </Label>
            <div
              className={`relative flex flex-col items-center justify-center w-full min-h-[120px] px-6 py-8 text-sm border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${
                dragActive
                  ? "border-primary bg-primary/5 scale-[1.02]"
                  : selectedFile
                  ? "border-green-300 bg-green-50/50 dark:border-green-700 dark:bg-green-950/20"
                  : "border-muted-foreground/25 bg-muted/20 hover:border-muted-foreground/40 hover:bg-muted/30"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Label
                htmlFor="file-upload"
                className="cursor-pointer w-full h-full flex flex-col items-center justify-center gap-3"
              >
                {selectedFile ? (
                  <>
                    <CheckCircle2 className="w-8 h-8 text-green-600 dark:text-green-400" />
                    <div className="text-center">
                      <p className="font-medium text-green-700 dark:text-green-300">
                        {selectedFile.name}
                      </p>
                      <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                        {formatFileSize(selectedFile.size)} • Ready to analyze
                      </p>
                    </div>
                  </>
                ) : (
                  <>
                    <Upload
                      className={`w-8 h-8 ${
                        dragActive ? "text-primary" : "text-muted-foreground"
                      }`}
                    />
                    <div className="text-center">
                      <p
                        className={`font-medium ${
                          dragActive ? "text-primary" : "text-foreground"
                        }`}
                      >
                        {dragActive
                          ? "Drop your file here"
                          : "Click to select or drag & drop"}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Supports: package.json, requirements.txt, pubspec.yaml
                      </p>
                    </div>
                  </>
                )}
              </Label>
              <Input
                id="file-upload"
                type="file"
                accept=".json,.txt,.yaml,.yml"
                onChange={handleFileUpload}
                className="sr-only"
              />
              {selectedFile && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-950"
                  onClick={clearFile}
                >
                  <X className="w-4 h-4 text-red-500" />
                </Button>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent
          value="manual"
          className="space-y-3 sm:space-y-4 mt-4 sm:mt-6"
        >
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label
                htmlFor="manual-input"
                className="text-sm sm:text-base font-medium"
              >
                Paste file content
              </Label>
              {hasContent && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => onFileContentChange("")}
                  className="h-8 px-2 text-xs text-muted-foreground hover:text-foreground"
                >
                  <X className="w-3 h-3 mr-1" />
                  Clear
                </Button>
              )}
            </div>
            <div className="relative">
              <Textarea
                id="manual-input"
                placeholder={`Paste your dependency file content here...

Examples:
• package.json: {"dependencies": {"react": "^18.0.0"}}
• requirements.txt: django==4.2.0
• pubspec.yaml: dependencies:\n  flutter:\n    sdk: flutter`}
                value={fileContent}
                onChange={(e) => onFileContentChange(e.target.value)}
                rows={10}
                className={`font-mono text-xs sm:text-sm min-h-[160px] sm:min-h-[200px] resize-none transition-all ${
                  hasContent
                    ? "border-green-300 bg-green-50/20 dark:border-green-700 dark:bg-green-950/10"
                    : ""
                }`}
              />
              {hasContent && (
                <div className="absolute bottom-3 right-3">
                  <Badge
                    variant="secondary"
                    className="text-xs bg-green-100 text-green-700 dark:bg-green-950 dark:text-green-300"
                  >
                    {fileContent.trim().split("\n").length} lines
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {isAnalyzing && (
        <div className="space-y-4 p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-3 text-sm sm:text-base">
            <Loader2 className="w-5 h-5 animate-spin text-blue-600 dark:text-blue-400" />
            <div className="flex-1">
              <p className="font-medium text-blue-900 dark:text-blue-100">
                Analyzing packages...
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                This may take a few moments depending on the number of
                dependencies
              </p>
            </div>
            <Badge
              variant="secondary"
              className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
            >
              {Math.round(analysisProgress)}%
            </Badge>
          </div>
          <Progress
            value={analysisProgress}
            className="h-2 bg-blue-100 dark:bg-blue-900"
          />
        </div>
      )}

      <Button
        onClick={onAnalyze}
        disabled={!canAnalyze}
        className={`w-full h-12 sm:h-14 text-base sm:text-lg font-medium transition-all duration-200 ${
          canAnalyze
            ? "bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg"
            : "bg-muted text-muted-foreground cursor-not-allowed"
        }`}
      >
        {isAnalyzing ? (
          <>
            <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 animate-spin" />
            Analyzing...
          </>
        ) : (
          <>
            <FileText className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" />
            Analyze Packages
          </>
        )}
      </Button>

      {error && (
        <Alert
          variant="destructive"
          className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20"
        >
          <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5" />
          <AlertDescription className="text-sm sm:text-base">
            <strong>Error:</strong> {error}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
