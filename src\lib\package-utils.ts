import { PackageInfo } from "@/types/package";

export function getPackageUrl(pkg: PackageInfo): string {
  switch (pkg.packageManager) {
    case "npm":
      return `https://www.npmjs.com/package/${pkg.name}`;
    case "pip":
      return `https://pypi.org/project/${pkg.name}/`;
    case "pub":
      return `https://pub.dev/packages/${pkg.name}`;
    default:
      return pkg.homepage || "#";
  }
}

export function getStatusColor(status: PackageInfo["status"]): string {
  switch (status) {
    case "up-to-date":
      return "bg-green-100 text-green-800 border-green-200";
    case "outdated":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "error":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

export function cleanVersion(version: string): string {
  return version
    .replace(/[\^~>=<]/g, "")
    .replace(/v/gi, "")
    .trim();
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
}