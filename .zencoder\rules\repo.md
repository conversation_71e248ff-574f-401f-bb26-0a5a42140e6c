---
description: Repository Information Overview
alwaysApply: true
---

# PackMan Information

## Summary

PackMan is a modern web application for analyzing and managing package dependencies across multiple package managers (npm, pip, pub.dev). It provides a user interface for uploading dependency files, analyzing package versions, and identifying outdated packages.

## Structure

- **src/**: Core application code (components, hooks, lib, types)
- **prisma/**: Database schema and configuration
- **public/**: Static assets
- **db/**: Database files
- **examples/**: Example implementations

## Language & Runtime

**Language**: TypeScript
**Version**: TypeScript 5.9.2
**Framework**: Next.js 15.4.6
**Build System**: Next.js build system
**Package Manager**: npm/pnpm

## Dependencies

**Main Dependencies**:

- React 19.1.1
- Next.js 15.4.6
- Prisma 6.13.0
- Socket.IO 4.8.1
- TanStack Query 5.84.2
- shadcn/ui (Radix UI components)
- Tailwind CSS 4.1.11
- Zod 4.0.17
- Zustand 5.0.7

**Development Dependencies**:

- ESLint 9.33.0
- TypeScript 5.9.2
- Nodemon 3.1.10

## Build & Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## Database

**ORM**: Prisma
**Database**: SQLite
**Schema**: User and Post models
**Commands**:

```bash
# Push schema changes
npm run db:push

# Generate Prisma client
npm run db:generate

# Create migrations
npm run db:migrate

# Reset database
npm run db:reset
```

## Server

**Type**: Custom Next.js server with Socket.IO integration
**Entry Point**: server.ts
**WebSockets**: Socket.IO for real-time communication
**Port**: 3000

## Main Features

- Package dependency analysis for npm, PyPI, and Pub.dev
- Version comparison between installed and latest packages
- File upload for package.json, requirements.txt, and pubspec.yaml
- Real-time updates via WebSockets
- Database storage for users and posts

## API Endpoints

- **/api/analyze-packages**: Analyzes package dependencies from uploaded files
- **/api/health**: Health check endpoint
- **WebSocket**: Available at /api/socketio for real-time communication

## Project Components

- **Package Analysis**: Services for checking package versions across different package managers
- **UI Components**: Comprehensive set of shadcn/ui components
- **Database Integration**: Prisma ORM for data persistence
- **WebSocket Server**: Real-time communication capabilities
