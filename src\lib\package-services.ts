// Services for native package manager APIs

export interface PackageVersionInfo {
  name: string;
  latestVersion: string;
  description?: string;
  homepage?: string;
  error?: string;
}

// NPM API Service
export class NPMService {
  private static readonly BASE_URL = 'https://registry.npmjs.org';

  static async getPackageInfo(packageName: string): Promise<PackageVersionInfo> {
    try {
      const response = await fetch(`${this.BASE_URL}/${packageName}`, {
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return {
            name: packageName,
            latestVersion: 'unknown',
            error: 'Package not found'
          };
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        name: packageName,
        latestVersion: data['dist-tags']?.latest || 'unknown',
        description: data.description,
        homepage: data.homepage || data.repository?.url
      };
    } catch (error) {
      console.error(`Error fetching NPM package ${packageName}:`, error);
      return {
        name: packageName,
        latestVersion: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// PyPI API Service
export class PyPIService {
  private static readonly BASE_URL = 'https://pypi.org/pypi';

  static async getPackageInfo(packageName: string): Promise<PackageVersionInfo> {
    try {
      const response = await fetch(`${this.BASE_URL}/${packageName}/json`, {
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return {
            name: packageName,
            latestVersion: 'unknown',
            error: 'Package not found'
          };
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const info = data.info;
      
      return {
        name: packageName,
        latestVersion: info.version || 'unknown',
        description: info.summary,
        homepage: info.home_page || info.project_urls?.Homepage
      };
    } catch (error) {
      console.error(`Error fetching PyPI package ${packageName}:`, error);
      return {
        name: packageName,
        latestVersion: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Pub.dev API Service
export class PubDevService {
  private static readonly BASE_URL = 'https://pub.dev/api';

  static async getPackageInfo(packageName: string): Promise<PackageVersionInfo> {
    try {
      const response = await fetch(`${this.BASE_URL}/packages/${packageName}`, {
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return {
            name: packageName,
            latestVersion: 'unknown',
            error: 'Package not found'
          };
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        name: packageName,
        latestVersion: data.latest?.version || 'unknown',
        description: data.description,
        homepage: data.homepage
      };
    } catch (error) {
      console.error(`Error fetching Pub.dev package ${packageName}:`, error);
      return {
        name: packageName,
        latestVersion: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

// Package Manager Factory
export class PackageManagerService {
  static async getPackageInfo(
    packageName: string, 
    packageManager: "npm" | "pip" | "pub"
  ): Promise<PackageVersionInfo> {
    switch (packageManager) {
      case "npm":
        return NPMService.getPackageInfo(packageName);
      case "pip":
        return PyPIService.getPackageInfo(packageName);
      case "pub":
        return PubDevService.getPackageInfo(packageName);
      default:
        return {
          name: packageName,
          latestVersion: 'unknown',
          error: 'Unsupported package manager'
        };
    }
  }

  static async getMultiplePackagesInfo(
    packages: Array<{ name: string; manager: "npm" | "pip" | "pub" }>
  ): Promise<PackageVersionInfo[]> {
    const promises = packages.map(pkg => 
      this.getPackageInfo(pkg.name, pkg.manager)
    );
    
    return Promise.all(promises);
  }
}