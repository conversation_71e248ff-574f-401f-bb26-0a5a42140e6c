"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Package } from "lucide-react";
import { PackageInfo, AnalysisSummary } from "@/types/package";
import { ModernHero } from "@/components/package-checker/ModernHero";
import { FileUploadSection } from "@/components/package-checker/FileUploadSection";
import { AnalysisStats } from "@/components/package-checker/AnalysisStats";
import { PackageResults } from "@/components/package-checker/PackageResults";
import { usePackageAnalysis } from "@/hooks/use-package-analysis";

export default function Home() {
  const [fileContent, setFileContent] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [results, setResults] = useState<PackageInfo[]>([]);
  const [summary, setSummary] = useState<AnalysisSummary | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { isAnalyzing, analysisProgress, analyzePackages } = usePackageAnalysis(
    {
      onAnalysisComplete: (newResults, newSummary) => {
        setResults(newResults);
        setSummary(newSummary);
        setError(null);
      },
      onError: (errorMessage) => {
        setError(errorMessage);
        setResults([]);
        setSummary(null);
      },
    }
  );

  const handleAnalyze = () => {
    analyzePackages(fileContent, selectedFile?.name || "unknown");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="max-w-6xl mx-auto space-y-6 sm:space-y-8">
        <ModernHero />

        <Card className="border-0 shadow-lg sm:shadow-xl" data-analyze-section>
          <CardHeader className="space-y-3 sm:space-y-4 pb-4 sm:pb-6">
            <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:gap-3 gap-2 text-lg sm:text-xl">
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5 sm:w-6 sm:h-6" />
                <span>Analyze Dependencies</span>
              </div>
            </CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Upload dependency files or paste content manually
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FileUploadSection
              fileContent={fileContent}
              selectedFile={selectedFile}
              onFileContentChange={setFileContent}
              onFileSelect={setSelectedFile}
              onAnalyze={handleAnalyze}
              isAnalyzing={isAnalyzing}
              analysisProgress={analysisProgress}
              error={error}
            />
          </CardContent>
        </Card>

        {results.length > 0 && summary && (
          <Card className="border-0 shadow-lg sm:shadow-xl">
            <CardHeader className="space-y-3 sm:space-y-4 pb-4 sm:pb-6">
              <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
                <Package className="w-5 h-5 sm:w-6 sm:h-6" />
                Analysis Results
              </CardTitle>
              <AnalysisStats summary={summary} />
            </CardHeader>
            <CardContent>
              <PackageResults results={results} />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
